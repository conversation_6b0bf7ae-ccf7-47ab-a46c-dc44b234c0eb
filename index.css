:root {
  --background-color: #121212;
  --surface-color: #1e1e1e;
  --sidebar-color: #181818;
  --primary-color: #bb86fc;
  --primary-variant-color: #3700b3;
  --secondary-color: #03dac6;
  --text-color: rgba(255, 255, 255, 0.87);
  --text-secondary-color: rgba(255, 255, 255, 0.6);
  --border-color: rgba(255, 255, 255, 0.12);
  --error-color: #cf6679;
  --font-body: 'Roboto', sans-serif;
  --font-story: '<PERSON>ra', serif;
  --sidebar-width: 280px;
  --header-height: 60px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-body);
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
  overflow: hidden;
}

#root {
  min-height: 100vh;
  width: 100%;
}

.app-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* --- Sidebar --- */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--sidebar-color);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    transition: margin-left 0.3s ease-in-out;
    z-index: 100;
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
}

.new-story-btn {
    width: 100%;
    background-color: transparent;
    border: 1px solid var(--text-secondary-color);
    color: var(--text-secondary-color);
    text-align: left;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.new-story-btn:hover {
    background-color: var(--surface-color);
    color: var(--text-color);
    border-color: var(--text-color);
}

.story-list {
    flex-grow: 1;
    overflow-y: auto;
}

.story-list ul {
    list-style: none;
}

.story-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    margin: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border: 1px solid transparent;
}

.story-item:hover, .story-item:focus {
    background-color: var(--surface-color);
    outline: none;
}

.story-item.active {
    background-color: var(--primary-variant-color);
    border-color: var(--primary-color);
}

.story-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.9rem;
    flex-grow: 1;
    pointer-events: none;
}

.delete-story-btn {
    background: none;
    border: none;
    color: var(--text-secondary-color);
    font-size: 1.2rem;
    line-height: 1;
    cursor: pointer;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-left: 0.5rem;
    opacity: 0;
    transition: opacity 0.2s, color 0.2s;
}

.story-item:hover .delete-story-btn,
.story-item.active .delete-story-btn {
    opacity: 1;
}

.delete-story-btn:hover {
    color: var(--error-color);
}


/* --- Chat Container --- */
.chat-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    position: relative;
    background-color: var(--background-color);
}

.chat-header {
  height: var(--header-height);
  display: flex;
  align-items: center;
  padding: 0 1rem;
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.main-title {
  font-family: var(--font-body);
  font-size: 1.25rem;
  font-weight: 500;
  text-align: center;
  color: var(--text-color);
  flex-grow: 1;
}

.chat-log {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.chat-message {
  display: flex;
  gap: 1rem;
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--surface-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.message-content {
  padding-top: 0.25rem;
  font-family: var(--font-story);
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--text-secondary-color);
}

.chat-message.user .message-content {
  color: var(--text-color);
}

.message-content p, 
.message-content h1, .message-content h2, .message-content h3 {
  margin-bottom: 1em;
}

.message-content p:last-child {
  margin-bottom: 0;
}


.welcome-screen {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 1.5rem;
  padding: 2rem;
  color: var(--text-secondary-color);
}

.welcome-screen h2 {
  font-family: var(--font-story);
  font-size: 2.5rem;
  color: var(--primary-color);
}

.prompt-suggestions {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
    max-width: 600px;
}

.prompt-suggestions button {
    background-color: var(--surface-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.prompt-suggestions button:hover {
    background-color: var(--primary-variant-color);
    border-color: var(--primary-color);
}


/* Chat Input */
.chat-input-area {
  padding: 1rem;
  padding-top: 0;
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
  flex-shrink: 0;
}

.chat-input-form {
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 8px;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
}

.chat-input-form:focus-within {
  border-color: var(--primary-color);
}

.chat-input-form textarea {
  flex-grow: 1;
  background: none;
  border: none;
  outline: none;
  color: var(--text-color);
  font-size: 1rem;
  line-height: 1.5;
  resize: none;
  max-height: 200px; /* Limit growth */
  overflow-y: auto;
}

.chat-input-form button {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
  border: none;
  border-radius: 6px;
  background-color: var(--primary-color);
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.chat-input-form button:hover:not(:disabled) {
  background-color: var(--secondary-color);
}

.chat-input-form button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--text-secondary-color);
}

.footer-text {
    font-size: 0.75rem;
    text-align: center;
    color: var(--text-secondary-color);
    margin-top: 0.5rem;
}


.error-message {
  color: var(--error-color);
  background-color: rgba(207, 102, 121, 0.1);
  border: 1px solid var(--error-color);
  padding: 1rem;
  border-radius: 4px;
  text-align: left;
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
}

/* --- Responsive & Toggles --- */
.sidebar-toggle {
    display: none;
}

@media (max-width: 768px) {
    .main-title {
        text-align: right;
        padding-right: 1rem;
    }

    .sidebar {
        position: absolute;
        height: 100%;
        left: 0;
        top: 0;
        margin-left: calc(-1 * var(--sidebar-width));
    }

    .sidebar.open {
        margin-left: 0;
        box-shadow: 10px 0 20px rgba(0,0,0,0.3);
    }
    
    .sidebar-toggle {
        display: flex;
        background: var(--surface-color);
        color: var(--text-color);
        border: 1px solid var(--border-color);
        width: 40px;
        height: 40px;
        font-size: 1.5rem;
        z-index: 101;
        align-items: center;
        justify-content: center;
        padding: 0;
        border-radius: 4px;
        cursor: pointer;
    }
}


@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
