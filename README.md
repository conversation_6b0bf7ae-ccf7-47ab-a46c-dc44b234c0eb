# AI Story Bot

An interactive storytelling application powered by free AI models. Create immersive stories with the help of AI - no API keys required!

## Features

- 🤖 **Multiple Free AI Providers**: Choose from BlackBox, Aryahcr, Nextway, and Alibaba
- 📚 **Story Management**: Create, save, and manage multiple story sessions
- 💬 **Interactive Chat Interface**: Collaborate with AI to build your stories
- 🎨 **Beautiful Dark Theme**: Optimized for comfortable reading and writing
- 📱 **Responsive Design**: Works on desktop and mobile devices
- 🔒 **No Authentication Required**: All providers work without API keys

## Run Locally

**Prerequisites:** Node.js

1. Install dependencies:
   ```bash
   npm install
   ```

2. Run the app:
   ```bash
   npm run dev
   ```

3. Open your browser and navigate to `http://localhost:5173`

## How to Use

1. **Select an AI Provider**: Choose from the dropdown in the sidebar
2. **Start a New Story**: Click "+ New Chat" or use one of the suggested prompts
3. **Collaborate**: Type your story prompts and let the AI continue the narrative
4. **Manage Stories**: Switch between different story sessions in the sidebar

## Available AI Providers

- **BlackBox**: Fast and reliable general-purpose AI
- **Aryahcr**: Supports GPT-3.5 and GPT-4 models
- **Nextway**: Multiple models with streaming support
- **Alibaba**: GPT-3.5 compatible with good performance

All providers are free to use and don't require any API keys or authentication.
