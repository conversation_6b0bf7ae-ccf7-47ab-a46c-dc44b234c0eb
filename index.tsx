import React, { useState, useRef, useEffect } from "react";
import { createRoot } from "react-dom/client";
import GPT4js from "gpt4js";
import { marked } from "marked";

// Define the structure for a chat message and a story session
type Message = {
  role: "user" | "model";
  text: string;
};

type Story = {
  id: string;
  title: string;
  messages: Message[];
};

// Available providers that don't require authentication
const AVAILABLE_PROVIDERS = [
  { id: "BlackBox", name: "BlackBox", description: "Fast and reliable" },
  { id: "Aryahcr", name: "Aryahcr", description: "GPT-3.5 & GPT-4 support" },
  { id: "Nextway", name: "Nextway", description: "Multiple models available" },
  { id: "Aliba<PERSON>", name: "Aliba<PERSON>", description: "GPT-3.5 support" },
];

const App = () => {
  // State for managing all stories and the current selection
  const [allStories, setAllStories] = useState<Story[]>([]);
  const [currentStoryId, setCurrentStoryId] = useState<string | null>(null);

  // State for UI and interaction
  const [prompt, setPrompt] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth > 768);
  const [selectedProvider, setSelectedProvider] = useState(AVAILABLE_PROVIDERS[0].id);

  // Refs for DOM elements
  const chatLogRef = useRef<HTMLDivElement | null>(null);

  // --- Effects ---

  // Load stories from localStorage on mount
  useEffect(() => {
    try {
      const storedStories = localStorage.getItem("ai-story-bot-chats");
      if (storedStories) {
        const parsedStories: Story[] = JSON.parse(storedStories);
        setAllStories(parsedStories);
        if (parsedStories.length > 0) {
          setCurrentStoryId(parsedStories[0].id);
        }
      }
    } catch (e) {
      console.error("Failed to load stories:", e);
      setError("Could not load stories from storage. They might be corrupted.");
      localStorage.removeItem("ai-story-bot-chats");
    }
  }, []);

  // Save stories to localStorage whenever they change
  useEffect(() => {
    if (allStories.length > 0) { // Only save if there are stories
        localStorage.setItem("ai-story-bot-chats", JSON.stringify(allStories));
    }
  }, [allStories]);

  // Auto-scroll chat log to the bottom
  useEffect(() => {
    if (chatLogRef.current) {
        chatLogRef.current.scrollTop = chatLogRef.current.scrollHeight;
    }
  }, [allStories, currentStoryId, isLoading]);

  // Handle window resizing for sidebar
  useEffect(() => {
    const handleResize = () => setIsSidebarOpen(window.innerWidth > 768);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // --- Derived State ---
  const currentStory = allStories.find(s => s.id === currentStoryId);

  // --- Handlers ---
  
  const handleNewStory = () => {
    setCurrentStoryId(null);
    setPrompt("");
    setError(null);
    if (window.innerWidth <= 768) {
        setIsSidebarOpen(false);
    }
  };

  const handleSelectStory = (storyId: string) => {
    setCurrentStoryId(storyId);
    setError(null);
    if (window.innerWidth <= 768) {
        setIsSidebarOpen(false);
    }
  };

  const handleDeleteStory = (e: React.MouseEvent, storyIdToDelete: string) => {
    e.stopPropagation();
    setAllStories(prev => prev.filter(s => s.id !== storyIdToDelete));
    if (currentStoryId === storyIdToDelete) {
      setCurrentStoryId(null);
      setPrompt("");
    }
  };

  const handleSendMessage = async (e?: React.FormEvent, customPrompt?: string) => {
    if (e) e.preventDefault();
    const messageText = customPrompt || prompt;
    if (!messageText.trim() || isLoading) return;

    setIsLoading(true);
    setError(null);
    if (!customPrompt) setPrompt("");

    const userMessage: Message = { role: "user", text: messageText };

    let storyToUpdateId = currentStoryId;

    // If this is the first message of a new story
    if (!storyToUpdateId) {
        const newId = Date.now().toString();
        const title = messageText.length > 35 ? messageText.substring(0, 32) + '...' : messageText;
        const newStory: Story = {
            id: newId,
            title: title || "Untitled Story",
            messages: [userMessage, { role: 'model', text: '' }], // Add placeholder for AI response
        };
        setAllStories(prev => [newStory, ...prev]);
        setCurrentStoryId(newId);
        storyToUpdateId = newId;
    } else {
        // Add new user message to the existing story
        setAllStories(prev => prev.map(s =>
            s.id === storyToUpdateId
                ? { ...s, messages: [...s.messages, userMessage, { role: 'model', text: '' }] }
                : s
        ));
    }
    
    try {
        // Prepare messages for gpt4js (include conversation history)
        const currentMessages = allStories.find(s => s.id === storyToUpdateId)?.messages || [];
        const messagesForApi = [
            { role: "system", content: "You are a master storyteller, known for weaving long, descriptive, and immersive narratives. Your task is to write a story based on the user's prompts. Each response you provide should be a substantial part of the ongoing story, equivalent to a detailed chapter in a book. Aim for responses that are several paragraphs long, rich in detail, character development, and plot progression. Continue the narrative fluidly, incorporating the user's requests and instructions seamlessly into the expansive world you are building." },
            ...currentMessages
                .filter(msg => msg.text.trim() !== '') // Exclude empty messages
                .map(msg => ({ role: msg.role === 'model' ? 'assistant' : msg.role, content: msg.text })),
            { role: "user", content: messageText }
        ];

        const options = {
            provider: selectedProvider,
            stream: true,
        };

        const provider = GPT4js.createProvider(selectedProvider);
        let modelResponse = "";

        await provider.chatCompletion(messagesForApi, options, (data: string) => {
            modelResponse += data;
            setAllStories(prev => prev.map(s => {
                if (s.id === storyToUpdateId) {
                    const updatedMessages = [...s.messages];
                    updatedMessages[updatedMessages.length - 1] = { role: 'model', text: modelResponse };
                    return { ...s, messages: updatedMessages };
                }
                return s;
            }));
        });
    } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "An unknown error occurred.";
        setError(errorMessage);
        // Add an error message to the chat
        setAllStories(prev => prev.map(s => {
            if (s.id === storyToUpdateId) {
                const updatedMessages = s.messages.slice(0, -1); // Remove placeholder
                return { ...s, messages: [...updatedMessages, { role: 'model', text: `Sorry, something went wrong: ${errorMessage}`}]};
            }
            return s;
        }))
    } finally {
        setIsLoading(false);
    }
  };

  // --- Render ---

  return (
    <div className="app-container">
      <aside className={`sidebar ${isSidebarOpen ? 'open' : ''}`}>
        <div className="sidebar-header">
            <button className="new-story-btn" onClick={handleNewStory}>
                + New Chat
            </button>
            <div className="provider-selection">
                <label htmlFor="provider-select">AI Provider:</label>
                <select
                    id="provider-select"
                    value={selectedProvider}
                    onChange={(e) => setSelectedProvider(e.target.value)}
                    className="provider-select"
                >
                    {AVAILABLE_PROVIDERS.map(provider => (
                        <option key={provider.id} value={provider.id}>
                            {provider.name} - {provider.description}
                        </option>
                    ))}
                </select>
            </div>
        </div>
        <nav className="story-list" aria-label="Chat history">
            <ul>
                {allStories.map(story => (
                    <li 
                        key={story.id}
                        className={`story-item ${currentStoryId === story.id ? 'active' : ''}`}
                        onClick={() => handleSelectStory(story.id)}
                        role="button"
                        tabIndex={0}
                        aria-current={currentStoryId === story.id ? 'page' : undefined}
                    >
                        <span className="story-title">{story.title}</span>
                        <button 
                            className="delete-story-btn"
                            onClick={(e) => handleDeleteStory(e, story.id)}
                            aria-label={`Delete chat titled ${story.title}`}
                        >
                            &times;
                        </button>
                    </li>
                ))}
            </ul>
        </nav>
      </aside>
      <div className="chat-container">
        <header className="chat-header">
            <button 
                className="sidebar-toggle" 
                onClick={() => setIsSidebarOpen(p => !p)}
                aria-label="Toggle sidebar"
                aria-expanded={isSidebarOpen}
            >
              {isSidebarOpen ? '‹' : '›'}
            </button>
            <h1 className="main-title">AI Story Bot</h1>
        </header>

        <main className="chat-log" ref={chatLogRef}>
          {!currentStory && !isLoading && (
            <div className="welcome-screen">
                <h2>Start a New Adventure</h2>
                <p>What tale shall we weave today? Give me a starting point.</p>
                <div className="prompt-suggestions">
                    <button onClick={() => handleSendMessage(undefined, "A lone astronaut on a forgotten planet discovers an ancient, sentient forest...")}>
                       "A lone astronaut on a forgotten planet..."
                    </button>
                    <button onClick={() => handleSendMessage(undefined, "In a city powered by alchemy, a young apprentice finds a forbidden spellbook.")}>
                       "In a city powered by alchemy..."
                    </button>
                    <button onClick={() => handleSendMessage(undefined, "The world's greatest detective receives a case from a ghost.")}>
                       "The world's greatest detective..."
                    </button>
                </div>
            </div>
          )}
        
          {currentStory?.messages.map((msg, index) => (
            <div key={index} className={`chat-message ${msg.role}`}>
                <div className="message-avatar">{msg.role === 'model' ? '🤖' : '👤'}</div>
                <div className="message-content" dangerouslySetInnerHTML={{ __html: marked.parse(msg.text + (isLoading && index === currentStory.messages.length - 1 ? '...' : '')) }} />
            </div>
          ))}

          {error && <div className="error-message">{error}</div>}
        </main>

        <footer className="chat-input-area">
          <form className="chat-input-form" onSubmit={handleSendMessage}>
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    handleSendMessage(e);
                }
              }}
              placeholder="Continue the story or give a new instruction..."
              rows={1}
              disabled={isLoading}
              aria-label="Chat input"
            />
            <button type="submit" disabled={isLoading || !prompt.trim()} aria-label="Send message">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24" height="24"><path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"></path></svg>
            </button>
          </form>
          <p className="footer-text">AI Story Bot. Fictional stories may be generated.</p>
        </footer>
      </div>
    </div>
  );
};

const container = document.getElementById("root");
if (container) {
  const root = createRoot(container);
  root.render(<App />);
}